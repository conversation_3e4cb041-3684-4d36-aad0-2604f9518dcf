# Arquitetura e Estratégia do Projeto

## Visão Geral

Este projeto é um fork do [Polar](https://github.com/polarsource/polar) com foco em expandir suporte para múltiplos gateways de pagamento, começando com Pagarme para o mercado brasileiro.

## Estrutura do Repositório

### Configuração Git

O projeto está configurado com dois remotes:

- **`origin`**: Aponta para `https://github.com/ismaelmcosta/polar-pay.git` (repositório principal)
- **`polar`**: Aponta para `https://github.com/polarsource/polar.git` (projeto original para acompanhar evoluções)

#### Comandos Úteis

```bash
# Enviar código para o repositório principal
git push

# Puxar atualizações do repositório principal
git pull

# Acompanhar atualizações do projeto original
git pull polar main

# Buscar atualizações do projeto original sem mesclar
git fetch polar
```

## Estratégia de Desenvolvimento

### Clients (Frontend)

O diretório `clients/` será **customizado** para atender às necessidades específicas do projeto. Este é o foco principal de desenvolvimento independente.

- **Localização**: `/clients`
- **Stack**: Turborepo, Next.js, TypeScript
- **Estratégia**: Desenvolvimento independente com customizações específicas

### Server (Backend)

O diretório `server/` será mantido **100% no padrão do Polar** para receber atualizações do projeto original.

- **Localização**: `/server`
- **Stack**: Python, FastAPI, SQLAlchemy, PostgreSQL
- **Estratégia**: Manter compatibilidade com o projeto original para receber updates

### Integrações de Pagamento

O projeto original Polar está associado apenas ao **Stripe** para receber pagamentos. Este fork adiciona suporte a múltiplos gateways de pagamento.

#### Arquitetura Atual

O sistema já possui uma estrutura preparada para múltiplos processors:

```python
# server/polar/enums.py
class PaymentProcessor(StrEnum):
    stripe = "stripe"
    # Novos processors serão adicionados aqui
```

#### Estrutura de Integrações

As integrações seguem o padrão:
- `/server/polar/integrations/stripe/` - Integração Stripe (padrão)
- `/server/polar/integrations/pagarme/` - Integração Pagarme (a ser criada)

Cada integração deve implementar:
- `service.py` - Serviço principal com métodos de pagamento
- `payment.py` - Handlers de webhooks e eventos de pagamento
- `endpoints.py` - Endpoints específicos da integração
- `schemas.py` - Schemas de dados
- `tasks.py` - Tarefas assíncronas (se necessário)

## Roadmap de Gateways de Pagamento

### Fase 1: Pagarme (Brasil) 🚧

**Status**: Planejamento

**Objetivos**:
- Adicionar `pagarme` ao enum `PaymentProcessor`
- Criar estrutura de integração em `/server/polar/integrations/pagarme/`
- Implementar métodos de pagamento principais:
  - Criação de pagamentos
  - Processamento de webhooks
  - Gerenciamento de métodos de pagamento
  - Suporte a cartões de crédito
  - Suporte a PIX (pagamento instantâneo brasileiro)
  - Suporte a boleto bancário

**Arquivos Principais a Modificar/Criar**:
- `server/polar/enums.py` - Adicionar `pagarme` ao enum
- `server/polar/integrations/pagarme/` - Nova estrutura de integração
- `server/polar/order/service.py` - Adicionar lógica de pagamento Pagarme
- `server/polar/checkout/service.py` - Adicionar suporte a checkout Pagarme
- `server/polar/payment/service.py` - Adicionar métodos de upsert Pagarme

### Fase 2: Outros Gateways (Futuro)

- Mercado Pago
- Asaas
- Outros gateways brasileiros

## Estrutura de Diretórios

```
polar/
├── clients/              # Frontend (CUSTOMIZADO)
│   ├── apps/
│   │   └── web/         # Dashboard Next.js
│   └── packages/
│       ├── checkout/    # Componentes de checkout
│       ├── ui/          # Componentes UI compartilhados
│       └── client/      # SDK TypeScript
│
├── server/              # Backend (MANTIDO PADRÃO POLAR)
│   ├── polar/
│   │   ├── integrations/
│   │   │   ├── stripe/  # Integração Stripe (padrão)
│   │   │   └── pagarme/ # Integração Pagarme (a criar)
│   │   ├── checkout/    # Serviços de checkout
│   │   ├── order/       # Serviços de pedidos
│   │   └── payment/     # Serviços de pagamento
│   └── migrations/      # Migrações do banco de dados
│
└── docs/                # Documentação
```

## Workflow de Desenvolvimento

### Para Desenvolvimento no Clients

1. Trabalhar normalmente no diretório `clients/`
2. Commits e pushes seguem o fluxo normal
3. Não há necessidade de sincronizar com o projeto original

### Para Atualizações do Server

1. **Receber atualizações do Polar original**:
   ```bash
   git fetch polar
   git merge polar/main
   ```

2. **Resolver conflitos** (se houver):
   - Manter customizações de integrações de pagamento
   - Aplicar atualizações do projeto original

3. **Testar integrações**:
   - Verificar que Stripe continua funcionando
   - Verificar que Pagarme não foi afetado

### Para Adicionar Novo Gateway de Pagamento

1. Adicionar ao enum `PaymentProcessor` em `server/polar/enums.py`
2. Criar estrutura em `server/polar/integrations/{gateway}/`
3. Implementar métodos principais:
   - Criação de pagamentos
   - Webhooks
   - Métodos de pagamento
4. Atualizar serviços:
   - `order/service.py` - Adicionar lógica no `trigger_payment`
   - `checkout/service.py` - Adicionar suporte no checkout
   - `payment/service.py` - Adicionar métodos de upsert
5. Criar migrações se necessário
6. Testes e documentação

## Considerações Importantes

### Manter Compatibilidade

- O código do `server/` deve manter compatibilidade com o projeto Polar original
- Customizações devem ser isoladas em módulos de integração
- Evitar modificar código core do Polar sem necessidade

### Sincronização com Polar Original

- Manter o remote `polar` atualizado regularmente
- Revisar changelog do Polar antes de fazer merge
- Testar após cada merge para garantir que nada quebrou

### Banco de Dados

- Migrações devem ser compatíveis com o schema do Polar
- Adicionar campos novos apenas quando necessário
- Documentar todas as mudanças no schema

## Contribuindo

Este projeto mantém o padrão do Polar para o backend, mas foca em customizações no frontend e adição de novos gateways de pagamento.

Para mais informações sobre desenvolvimento, consulte:
- [DEVELOPMENT.md](./DEVELOPMENT.md) - Guia de desenvolvimento
- [README.md](./README.md) - Documentação principal

