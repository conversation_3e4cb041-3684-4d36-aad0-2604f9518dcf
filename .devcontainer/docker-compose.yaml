version: "3.8"

services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile

    volumes:
      - ..:/workspace:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db

  redis:
    image: redis:alpine
    restart: unless-stopped
    network_mode: service:db

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: polar
      POSTGRES_DB: polar
      POSTGRES_PASSWORD: polar

  ingress:
    image: caddy:2.7
    network_mode: service:db
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile

volumes:
  postgres-data:
