import ArrowOutward from '@mui/icons-material/ArrowOutward'
import Button from '@polar-sh/ui/components/atoms/Button'
import Link from 'next/link'
import { SplitPromo } from './molecules/SplitPromo'

export const MerchantOfRecord = () => {
  return (
    <SplitPromo
      title="Pluggou como Merchant of Record"
      description="Esqueça tudo sobre cobrança e impostos. Cuidamos de tudo para você como merchant of record."
      bullets={[
        'Impostos sobre Vendas, IVA, GST, etc.',
        'Saque com Stripe Connect',
        'Livro-razão Detalhado de Transações',
      ]}
      image="/assets/landing/transactions.jpg"
      cta1={
        <Link href="/resources/merchant-of-record">
          <Button variant="secondary" className="rounded-full">
            Saiba mais
            <span className="ml-2">
              <ArrowOutward fontSize="inherit" />
            </span>
          </Button>
        </Link>
      }
    />
  )
}
