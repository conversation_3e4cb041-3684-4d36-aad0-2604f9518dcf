const CurlIcon = ({ size = 100 }: { size?: number }) => (
  <svg
    clipRule="evenodd"
    fillRule="evenodd"
    strokeLinejoin="round"
    strokeMiterlimit="2"
    viewBox="0 0 560 400"
    xmlns="http://www.w3.org/2000/svg"
    width={size}
  >
    <g fillRule="nonzero">
      <path
        d="m450.484 160.695c-2.875 0-5.204-2.329-5.204-5.204s2.329-5.205 5.204-5.205 5.204 2.33 5.204 5.205-2.329 5.204-5.204 5.204m-51.73 88.98c-2.875 0-5.204-2.329-5.204-5.204s2.329-5.205 5.204-5.205 5.204 2.33 5.204 5.205-2.329 5.204-5.204 5.204m51.73-103.67c-5.267 0-9.528 4.271-9.528 9.528 0 1.123.284 2.172.639 3.169l-44.699 76.598c-4.323.905-7.629 4.554-7.629 9.15 0 5.267 4.271 9.527 9.528 9.527s9.527-4.27 9.527-9.527c0-1.06-.283-2.025-.598-2.98l44.91-76.913c4.187-1.004 7.366-4.596 7.366-9.087 0-5.267-4.271-9.528-9.528-9.528"
        fill="currentColor"
      />
      <g fill="currentColor">
        <path d="m411.66 160.695c-2.875 0-5.204-2.329-5.204-5.204s2.329-5.205 5.204-5.205 5.205 2.33 5.205 5.205-2.33 5.204-5.205 5.204m-51.73 88.98c-2.875 0-5.204-2.329-5.204-5.204s2.329-5.205 5.204-5.205 5.205 2.33 5.205 5.205-2.33 5.204-5.205 5.204m51.73-103.67c-5.267 0-9.527 4.271-9.527 9.528 0 1.123.283 2.172.639 3.169l-44.7 76.598c-4.323.905-7.639 4.554-7.639 9.15 0 5.267 4.271 9.527 9.528 9.527 5.267 0 9.527-4.27 9.527-9.527 0-1.06-.283-2.025-.598-2.98l44.91-76.913c4.186-1.004 7.366-4.596 7.366-9.087 0-5.267-4.271-9.528-9.528-9.528" />
        <path d="m340.309 176.645c2.875 0 5.204 2.329 5.204 5.204s-2.329 5.205-5.204 5.205-5.205-2.33-5.205-5.205 2.33-5.204 5.205-5.204m0 14.69c5.267 0 9.527-4.271 9.527-9.528 0-1.049-.286-2.025-.602-2.98-1.259-3.788-4.722-6.558-8.93-6.558-.672 0-1.259.25-1.899.385-4.323.901-7.628 4.554-7.628 9.139 0 5.268 4.27 9.528 9.527 9.528" />
        <path d="m335.062 215.154c0-2.876 2.33-5.205 5.205-5.205s5.204 2.329 5.204 5.205c0 2.875-2.329 5.204-5.204 5.204s-5.205-2.329-5.205-5.204m14.69 0c0-1.06-.286-2.026-.602-2.98-1.259-3.788-4.722-6.559-8.93-6.559-.672 0-1.259.25-1.899.381-4.323.906-7.628 4.554-7.628 9.15 0 5.257 4.271 9.528 9.527 9.528 5.268 0 9.528-4.271 9.528-9.528" />
        <path d="m100 225.937c0 7.439 1.396 11.122 7.198 15.844l8.951 7.46c5.676 4.659 8.572 4.785 15.634 4.785h19.202c4.029 0 8.331-.885 8.331-6.054 0-5.31-4.784-6.065-8.331-6.065h-21.301c-2.413 0-4.805-1.259-6.946-3.033l-7.313-6.18c-1.9-1.511-3.274-2.77-3.274-5.162v-15.005c0-2.393 1.374-3.652 3.274-5.184l7.313-6.18c2.151-1.752 4.544-3.022 6.946-3.022h21.301c3.547 0 8.331-.77 8.331-6.065 0-5.162-4.291-6.054-8.331-6.054h-19.202c-7.062 0-9.968.131-15.634 4.784l-8.951 7.44c-5.802 4.806-7.198 8.457-7.198 15.949z" />
        <path d="m224.898 194.353c0-4.166-.885-8.331-6.054-8.331-5.184 0-6.065 4.165-6.065 8.331v35.151l-17.838 11.228c-1.133.754-2.392 1.133-3.788 1.133h-4.029c-2.907 0-6.055-.755-7.062-2.529-1.017-1.752-1.133-5.047-1.259-7.817l-1.259-37.25c-.131-3.914-.755-8.331-5.939-8.331-5.54 0-6.18 4.669-6.055 9.087l1.26 38.089c.245 6.558.131 11.752 5.561 16.369 4.029 3.389 7.817 4.407 11.227 4.407h8.951c3.273 0 6.558-1.511 10.472-4.03l9.842-6.442v2.014c0 4.166.885 8.458 6.054 8.458 6.443 0 5.939-6.181 5.939-8.951z" />
        <path d="m231.227 245.726c0 4.166.885 8.332 6.054 8.332 5.184 0 6.065-4.166 6.065-8.332v-30.639l16.894-13.956c2.151-1.773 3.788-2.906 6.054-2.906h3.547c2.906 0 5.802 0 7.313 2.151 1.259 1.752 1.259 3.274 1.259 5.299 0 3.903 1.774 7.439 6.181 7.439 5.183 0 5.939-4.291 5.939-8.457 0-5.54-1.396-9.706-5.425-13.746-3.274-3.273-7.177-4.784-11.857-4.784h-9.727c-3.788 0-7.314 2.392-10.703 5.162l-9.464 7.838v-4.931c0-4.03-1.26-8.069-6.065-8.069-5.541 0-6.055 4.543-6.055 8.95z" />
        <path d="m312.768 169.485c0-7.198-2.014-8.457-8.95-8.457h-12.697c-4.05 0-8.331.885-8.331 6.054 0 5.309 4.785 6.065 8.331 6.065h9.591v68.728h-9.591c-4.05 0-8.331.886-8.331 6.065 0 5.299 4.785 6.055 8.331 6.055h30.745c3.546 0 8.331-.755 8.331-6.055 0-5.183-4.281-6.065-8.331-6.065h-9.087z" />
      </g>
    </g>
  </svg>
)

export default CurlIcon
