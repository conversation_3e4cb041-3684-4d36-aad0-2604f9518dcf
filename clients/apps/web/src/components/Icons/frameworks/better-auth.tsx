const BetterAuthIcon = ({ size = 40 }: { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 300 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0" y="0" width="86.9879" height="259" fill="currentColor" />
      <rect
        x="268.575"
        y="0"
        width="92.4247"
        height="259"
        fill="currentColor"
      />
      <rect
        x="358.282"
        y="0"
        width="83.4555"
        height="174.52"
        transform="rotate(90 358.282 0)"
        fill="currentColor"
      />
      <rect
        x="361"
        y="175.544"
        width="83.4555"
        height="177.238"
        transform="rotate(90 361 175.544)"
        fill="currentColor"
      />
      <rect
        x="183.762"
        y="83.455"
        width="92.0888"
        height="96.7741"
        transform="rotate(90 183.762 83.455)"
        fill="currentColor"
      />
    </svg>
  )
}

export default BetterAuthIcon
