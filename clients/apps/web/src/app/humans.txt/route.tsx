export async function GET() {
  return new Response(
    `
                             ::-=+**########**++=:.                             
                        :=+##########################+=:                        
                    .=*########*#########################*=.                    
                 .=########+-=*#######***###########*-*######=:                 
              :=*######+:.:=######*-.     .-*########*..=#######=:              
            :*######+:  -###*=###=           :*#######*   +########-            
          :*######+.  .+###-.###:             .*#######+   +#####+###:          
         +######+.   =###*. *##:                +#######:   +#####:+##+.        
       .*#####*.   .*###-  *##-                  #######*    +####* -###-       
      =######=    :####-  +##=                   :#######-    *####* :###+      
    .######*     +###*   =###                     =#######     #####=  *##*     
    *#####+     +####.  :###+                      *######:    =####*  .####.   
   =#####+     +####.   +###:                      -######-    :#####.  :###+   
  :#####*     =####-    ####                       .######=     #####-   =###=  
 .######.    :####*    =###=                        +#####+     *####=   .####: 
 +#####:     *####.    *###.                        .#####*     *####+    *###* 
 #####+     +####+    :####                          #####*     +####+    +####.
-#####:    :#####     *###*                          *####*     +####+    =####:
+#####     =####+     ####*                          +####+     *####=    -####=
#####=     #####-    :####*                          +####=     #####-    -####+
#####:    -#####.    -####*                          =####-    .#####.    =####+
*####.    +#####     =####*                          =####:    -####*     *####=
=####:    #####*     =#####                          +####.    +####=     #####-
.####=    #####*     =#####.                         *###*     #####.    -#####.
 +####    ######     =#####=                         ####.    *####:     *####* 
 :####:   *#####     =######                        .###+    =####=     *#####- 
  =###+   =#####.    -######:                       +###-   .####+     =#####*  
   *###:  .#####-    :######=                       ####   .####*     -######   
   .####.  *####+     ######*                      :###.   *####     -######:   
    :###*  -#####:    +######:                     *##=   =###*.    -#####*.    
      +##*  +#####.    ######*                    =##*  .*###=    .*#####*      
       =###: *####+    +######*                  :###: .*###:    =######+       
        :###=.#####:   :#######=                 *##- =###*.   .#######-        
         .+##*=#####:   *#######+               +##- =###-   .+######=          
           .-*#######+. .########*.           :*#*==##*-  .=*######=.           
              -*#######=  *########+:       :*###*##*- .-*######*:              
                .=*#####*-.*##########+--=+#######+:.-*#######+:                
                   :=*#####+###################**=*########=:                   
                       :+*############################*+-.                      
                           .:-==+**#########***+==-:.                           



              Polar is made by all of our wonderful contributors.

                    https://github.com/polarsource/polar

                Wanna work with us? https://polar.sh/company#open-roles

    `,
    {
      headers: {
        // 'Content-Type': 'image/svg+xml',
        'Cache-Control': 'no-cache',
      },
      status: 200,
    },
  )
}
