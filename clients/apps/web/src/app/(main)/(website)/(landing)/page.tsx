import { Metadata } from 'next'
import LandingPage from '../../../../components/Landing/LandingPage'

export const metadata: Metadata = {
  title: 'Pluggou — Infraestrutura de pagamentos para o século 21',
  description: 'Infraestrutura de pagamentos para o século 21',
  keywords:
    'monetiza<PERSON>, merchant of record, saas, produtos digitais, plataforma, desenvolvedor, c<PERSON>digo aberto, financiamento, economia',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <LandingPage />
}
