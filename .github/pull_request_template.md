<!--
Thank you for contributing to Polar! 🎉

Before submitting your PR, please ensure:
- An issue exists and you're assigned to it (unless it's a minor fix)
- You've tested your changes locally
- All tests pass
- Code follows our style guidelines

For minor fixes (typos, broken links, formatting), you may skip the issue requirement.
-->

## 📋 Summary

**Related Issue**: Fixes #<!-- issue number -->

<!-- Brief description of what this PR does -->

## 🎯 What

<!-- Describe what changes you've made -->

## 🤔 Why

<!-- Explain why this change is needed -->

## 🔧 How

<!-- Describe the approach you took to implement the changes -->

## 🧪 Testing

<!-- Check all that apply -->

- [ ] I have tested these changes locally
- [ ] All existing tests pass (`uv run task test` for backend, `pnpm test` for frontend)
- [ ] I have added new tests for new functionality
- [ ] I have run linting and type checking (`uv run task lint && uv run task lint_types` for backend)

### Test Instructions

<!-- Provide step-by-step instructions for reviewers to test your changes -->

1.
2.
3.

## 🖼️ Screenshots/Recordings

<!-- Include screenshots or recordings if your changes affect the UI -->
<!-- You can drag and drop images directly into this text area -->

## 📝 Additional Notes

<!-- Any additional context, considerations, or notes for reviewers -->

## ✅ Pre-submission Checklist

- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code where necessary
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have updated the relevant tests
- [ ] All tests pass locally
- [ ] **AI/LLM Policy**: If I used AI assistance, I have tested and executed the code locally (not just "vibe-coded")
