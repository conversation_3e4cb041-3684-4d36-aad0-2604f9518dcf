# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.

version: 2
updates:
- package-ecosystem: "github-actions" # See documentation for possible values
  directory: "/" # Location of package manifests
  schedule:
    interval: "weekly"
- package-ecosystem: "pip"
  directory: "/server"
  schedule:
    interval: "weekly"
