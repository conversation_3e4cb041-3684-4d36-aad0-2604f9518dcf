name: Update docs OpenAPI schema and webhooks

on:
  workflow_dispatch:
  schedule:
    - cron: "0 8 * * *"

concurrency:
  group: docs-update-openapi-schema
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ${{ github.workspace }}/docs

    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Run update script
        run: ./update-schema.sh https://spec.speakeasy.com/polar/polar/polar-oas-with-code-samples

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          add-paths: docs/
          commit-message: "docs: update OpenAPI schema"
          title: "docs: update OpenAPI schema"
          branch: "docs/update-openapi-schema"
          delete-branch: true
