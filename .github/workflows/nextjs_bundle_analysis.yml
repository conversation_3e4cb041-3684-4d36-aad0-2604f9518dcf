# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

name: "Client: Next.JS Bundle Analyzis 🧐"

on:
  pull_request:
    paths:
      - "clients/**"
      - ".github/workflows/nextjs_bundle_analysis.yml"
  push:
    branches:
      - main # change this if your default branch is named differently
  workflow_dispatch:

defaults:
  run:
    working-directory: ./clients/apps/web

permissions:
  contents: read # for checkout repository
  actions: read # for fetching base branch bundle stats
  pull-requests: write # for comments

jobs:
  analyze:
    runs-on: ubuntu-latest

    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      VERCEL_ENV: "testing"

    steps:
      - uses: actions/checkout@v5

      - uses: pnpm/action-setup@v4
        with:
          package_json_file: clients/package.json

      - name: Setup Node.js environment
        uses: actions/setup-node@v6
        with:
          node-version-file: clients/.node-version
          cache: "pnpm"
          cache-dependency-path: "clients/pnpm-lock.yaml"

      - name: Install dependencies
        working-directory: ./clients
        run: pnpm install

      - name: Restore next build
        uses: actions/cache@v4
        id: restore-build-cache
        env:
          cache-name: cache-next-build
        with:
          # if you use a custom build directory, replace all instances of `.next` in this file with your build directory
          # ex: if your app builds to `dist`, replace `.next` with `dist`
          path: .next/cache
          # change this if you prefer a more strict cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}

      - name: Build next.js app
        working-directory: ./clients
        run: pnpm build

      # Here's the first place where next-bundle-analysis' own script is used
      # This step pulls the raw bundle stats for the current bundle
      - name: Analyze bundle
        run: npx -p nextjs-bundle-analysis report

      - name: Upload bundle
        uses: actions/upload-artifact@v5
        with:
          name: bundle
          path: ./clients/apps/web/.next/analyze/__bundle_analysis.json

      - name: Download base branch bundle stats
        uses: dawidd6/action-download-artifact@v11
        if: success() && github.event.number
        with:
          workflow: nextjs_bundle_analysis.yml
          branch: ${{ github.event.pull_request.base.ref }}
          path: ./clients/apps/web/.next/analyze/base

      # And here's the second place - this runs after we have both the current and
      # base branch bundle stats, and will compare them to determine what changed.
      # There are two configurable arguments that come from package.json:
      #
      # - budget: optional, set a budget (bytes) against which size changes are measured
      #           it's set to 350kb here by default, as informed by the following piece:
      #           https://infrequently.org/2021/03/the-performance-inequality-gap/
      #
      # - red-status-percentage: sets the percent size increase where you get a red
      #                          status indicator, defaults to 20%
      #
      # Either of these arguments can be changed or removed by editing the `nextBundleAnalysis`
      # entry in your package.json file.
      - name: Compare with base branch bundle
        if: success() && github.event.number
        run: ls -laR .next/analyze/base && npx -p nextjs-bundle-analysis compare

      - name: Get Comment Body
        id: get-comment-body
        if: success() && github.event.number
        # https://docs.github.com/en/actions/using-workflows/workflow-commands-for-github-actions#multiline-strings
        run: |
          echo "body<<EOF" >> $GITHUB_OUTPUT
          echo "$(cat .next/analyze/__bundle_analysis_comment.txt)" >> $GITHUB_OUTPUT
          echo EOF >> $GITHUB_OUTPUT

      - name: Find Comment
        uses: peter-evans/find-comment@v4
        if: success() && github.event.number
        id: fc
        with:
          issue-number: ${{ github.event.number }}
          body-includes: "<!-- __NEXTJS_BUNDLE -->"

      - name: Create Comment
        uses: peter-evans/create-or-update-comment@v5
        if: success() && github.event.number && steps.fc.outputs.comment-id == 0
        with:
          issue-number: ${{ github.event.number }}
          body: ${{ steps.get-comment-body.outputs.body }}

      - name: Update Comment
        uses: peter-evans/create-or-update-comment@v5
        if: success() && github.event.number && steps.fc.outputs.comment-id != 0
        with:
          issue-number: ${{ github.event.number }}
          body: ${{ steps.get-comment-body.outputs.body }}
          comment-id: ${{ steps.fc.outputs.comment-id }}
          edit-mode: replace
