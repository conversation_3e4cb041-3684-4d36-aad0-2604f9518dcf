name: Client

on:
  push:
    branches: ["main"]
  pull_request:
    types: [opened, synchronize, edited]

defaults:
  run:
    working-directory: ./clients

permissions:
  contents: read

jobs:
  build:
    name: "Client: Tests 🎨"
    timeout-minutes: 15
    runs-on: ubuntu-latest

    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      VERCEL_ENV: "testing"

    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - uses: pnpm/action-setup@v4
        with:
          package_json_file: clients/package.json

      - name: Setup Node.js environment
        uses: actions/setup-node@v6
        with:
          node-version-file: clients/.node-version
          cache: "pnpm"
          cache-dependency-path: "clients/pnpm-lock.yaml"

      - name: Install dependencies
        run: pnpm install

      - name: Lint
        run: pnpm run lint

      - name: Typecheck
        run: pnpm run typecheck

      - name: Build
        run: pnpm build
