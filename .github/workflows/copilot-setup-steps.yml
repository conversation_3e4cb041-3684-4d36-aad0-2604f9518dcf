name: "Copilot Setup Steps"

# Automatically run the setup steps when they are changed to allow for easy validation, and
# allow manual testing through the repository's "Actions" tab
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # The job MUST be called `copilot-setup-steps` or it will not be picked up by Copilot.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Set the permissions to the lowest permissions possible needed for your steps.
    # Copilot will be given its own token for its operations.
    permissions:
      # If you want to clone the repository as part of your setup steps, for example to install dependencies, you'll need the `contents: read` permission. If you don't clone the repository in your setup steps, <PERSON><PERSON><PERSON> will do this for you automatically after the steps complete.
      contents: read

    services:
      postgres:
        image: postgres:15.1-bullseye
        env:
          POSTGRES_USER: polar
          POSTGRES_PASSWORD: polar
          POSTGRES_DB: polar_development
          POSTGRES_PORT: 5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      minio:
        image: bitnamilegacy/minio:2024.5.28
        ports:
          - 9000:9000
          - 9001:9001
        env:
          MINIO_ROOT_USER: polar
          MINIO_ROOT_PASSWORD: polarpolar
        options: >-
          --health-cmd "curl -I http://127.0.0.1:9000/minio/health/live"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    # You can define any steps you want, and they will run before the agent starts.
    # If you do not check out your code, Copilot will do this for you.
    steps:
      - uses: actions/checkout@v5

      - uses: actions/cache@v4
        with:
          path: |
            server/.mypy_cache
          key: mypy-${{ runner.os }}-${{ github.sha }}
          restore-keys: |
            mypy-${{ runner.os }}-
            mypy-

      - name: 💿 MinIO Setup
        working-directory: ./server/.minio
        env:
          MINIO_HOST: 127.0.0.1
          MINIO_ROOT_USER: polar
          MINIO_ROOT_PASSWORD: polarpolar
          ACCESS_KEY: polar-development
          SECRET_ACCESS_KEY: polar123456789
          BUCKET_NAME: polar-s3
          BUCKET_TESTING_NAME: testing-polar-s3
          POLICY_FILE: policy.json
        run: bash github.sh

      - uses: pnpm/action-setup@v4
        with:
          package_json_file: server/emails/package.json

      - name: 📬 Setup Node.js environment for server/emails
        uses: actions/setup-node@v6
        with:
          node-version-file: server/emails/.node-version
          cache: "pnpm"
          cache-dependency-path: "clients/pnpm-lock.yaml"

      - name: 📬 Build server/emails
        working-directory: server/emails
        run: pnpm install --frozen-lockfile && pnpm build

      - name: Install uv
        uses: astral-sh/setup-uv@v7
        with:
          enable-cache: true

      - name: Set up Python
        uses: actions/setup-python@v6
        with:
          python-version-file: "server/pyproject.toml"

      - name: 🔧 uv install
        working-directory: ./server
        run: |
          uv sync --dev
          uv run task generate_dev_jwks

      - name: 📚 Setup documentation
        working-directory: ./docs
        run: |
          pnpm install --frozen-lockfile
          pnpm exec mintlify update
