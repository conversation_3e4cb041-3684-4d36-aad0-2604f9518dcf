name: Build and Deploy

on:
  push:
    branches: ["main"]
    paths:
      - "server/**"
      - "clients/**"
      - ".github/workflows/deploy.yml"
      - ".github/workflows/deploy-environment.yml"
      - ".github/workflows/deploy_server.sh"
  workflow_dispatch:

jobs:
  changes:
    name: "Detect Changes 🔍"
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v5
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            backend:
              - 'server/**'
              - '.github/workflows/deploy.yml'
              - '.github/workflows/deploy-environment.yml'
              - '.github/workflows/deploy_server.sh'
            frontend:
              - 'clients/**'
              - '.github/workflows/deploy.yml'
              - '.github/workflows/deploy-environment.yml'

  build:
    name: "Build Docker Image 🐳"
    needs: changes
    if: needs.changes.outputs.backend == 'true' || github.event_name == 'workflow_dispatch'
    timeout-minutes: 15
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      digest: ${{ steps.push.outputs.digest }}
    steps:
      - uses: actions/checkout@v5

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/polarsource/polar
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
            type=raw,value=latest,enable={{is_default_branch}}

      - uses: docker/build-push-action@v6
        id: push
        with:
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          context: ./server
          platforms: linux/amd64
          build-args: |
            RELEASE_VERSION=${{ github.sha }}
          secrets: |
            IPINFO_ACCESS_TOKEN=${{ secrets.IPINFO_ACCESS_TOKEN }}

  deploy-sandbox:
    name: "Deploy to Sandbox 🧪"
    needs: [changes, build]
    if: always() && (needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' || github.event_name == 'workflow_dispatch')
    uses: ./.github/workflows/deploy-environment.yml
    with:
      environment: sandbox
      docker-digest: ${{ needs.build.outputs.digest }}
      render-service-ids: "srv-crkocgbtq21c73ddsdbg,srv-d089jj7diees73934kgg"
      skip-backend: ${{ needs.changes.outputs.backend != 'true' && github.event_name != 'workflow_dispatch' }}
      skip-frontend: ${{ needs.changes.outputs.frontend != 'true' && github.event_name != 'workflow_dispatch' }}
    secrets:
      RENDER_API_TOKEN: ${{ secrets.RENDER_API_TOKEN }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_ORG: ${{ secrets.SENTRY_ORG }}

  deploy-production:
    name: "Deploy to Production 🚀"
    needs: [changes, build, deploy-sandbox]
    if: always() && !failure() && !cancelled() && (needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' || github.event_name == 'workflow_dispatch')
    uses: ./.github/workflows/deploy-environment.yml
    with:
      environment: production
      docker-digest: ${{ needs.build.outputs.digest }}
      render-service-ids: "srv-ci4r87h8g3ne0dmvvl60,srv-d089jj7diees73934ka0,srv-d3hrh1j3fgac73a1t4r0"
      skip-backend: ${{ needs.changes.outputs.backend != 'true' && github.event_name != 'workflow_dispatch' }}
      skip-frontend: ${{ needs.changes.outputs.frontend != 'true' && github.event_name != 'workflow_dispatch' }}
    secrets:
      RENDER_API_TOKEN: ${{ secrets.RENDER_API_TOKEN }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
