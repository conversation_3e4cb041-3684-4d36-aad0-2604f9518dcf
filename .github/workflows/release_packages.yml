name: Release packages

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ${{ github.workspace }}/clients

    permissions:
      contents: write
      pull-requests: write
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - uses: pnpm/action-setup@v4
        with:
          package_json_file: clients/package.json

      - name: Setup Node.js environment
        uses: actions/setup-node@v6
        with:
          node-version-file: clients/.node-version
          cache: "pnpm"
          cache-dependency-path: "clients/pnpm-lock.yaml"

      - name: Install dependencies
        run: pnpm install

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        # Pin to last known working version (see: https://github.com/changesets/action/issues/501)
        uses: changesets/action@v1.5.1
        with:
          # This expects you to have a script called release which does a build for your packages and calls changeset publish
          publish: pnpm release-packages
          cwd: ${{ github.workspace }}/clients
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          POLAR_CHECKOUT_EMBED_SCRIPT_ALLOWED_ORIGINS: "https://polar.sh,https://sandbox.polar.sh"
