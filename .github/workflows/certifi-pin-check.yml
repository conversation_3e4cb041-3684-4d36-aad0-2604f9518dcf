name: Check if certifi<=2025.1.31 is still needed

on:
  workflow_dispatch:
  schedule:
    - cron: "0 9 * * *"

permissions:
  contents: read

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install uv
        uses: astral-sh/setup-uv@v7
        with:
          enable-cache: true

      - name: Run certifi pin check. Fail if certifi>2025.1.31 is not needed anymore.
        run: |
          .github/workflows/certifi-pin-check.py ${{ secrets.CERTIFI_PIN_URL_CHECK }}
